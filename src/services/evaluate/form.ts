import type { DataParams } from 'src/types/data';
import { api } from 'src/boot/axios';
import type { Assessment } from 'src/types/models';
import type { ChartData } from 'src/types/chart';
import { Notify } from 'quasar';
import { useGlobalStore } from 'src/stores/global';
const globalStore = useGlobalStore();
const showError = (message: string) => {
  Notify.create({
    message: message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};

const getAll = async (params: DataParams, programId: number): Promise<{ data: Assessment[] }> => {
  try {
    return await api.post('http://localhost:3000/evaluate/assessments/list', {
      ...params,
      programId,
    });
  } catch (error: unknown) {
    showError('ไม่สามารถโหลดรายการแบบประเมินได้');
    throw error;
  }
};

const getFormById = async (id: number): Promise<{ data: Assessment }> => {
  try {
    // Use the same endpoint pattern as AssessmentService
    return await api.get(`/evaluate/assessments/${id}`);
  } catch (error) {
    showError('ไม่สามารถโหลดฟอร์มแบบประเมินได้');
    throw error;
  }
};

const getOne = async (id: number): Promise<{ data: Assessment }> => {
  try {
    // Use the same endpoint pattern as AssessmentService
    return await api.get(`/evaluate/assessments/${id}`);
  } catch (error) {
    showError('ไม่สามารถดึงข้อมูลแบบประเมินได้');
    throw error;
  }
};

const createForm = async (data: Partial<Assessment>): Promise<{ data: Assessment }> => {
  try {
    globalStore.Loading();
    // Use the same endpoint pattern as AssessmentService
    return await api.post('/evaluate/assessments', data);
  } catch (error) {
    showError('ไม่สามารถสร้างแบบประเมินได้');
    throw error;
  }
};

const updateForm = async (id: number, data: Assessment): Promise<{ data: Assessment }> => {
  try {
    globalStore.Loading();
    // Use the same endpoint pattern as AssessmentService
    return await api.patch(`/evaluate/assessments/${id}`, data);
  } catch (error) {
    showError('ไม่สามารถอัปเดตแบบประเมินได้');
    throw error;
  }
};

const deleteForm = async (id: number): Promise<void> => {
  try {
    // Use the same endpoint pattern as AssessmentService
    await api.delete(`/evaluate/assessments/${id}`);
    Notify.create({ message: 'ลบเรียบร้อยแล้ว', type: 'positive' });
  } catch (error) {
    showError('ไม่สามารถลบแบบประเมินได้');
    throw error;
  }
};

const getResponseById = async (id: number): Promise<{ data: ChartData[] }> => {
  try {
    return api.get(`http://localhost:3000/evaluate/responses/${id}`);
  } catch (error) {
    showError('ไม่สามารถโหลดคำตอบของแบบประเมินได้');
    throw error;
  }
};

const getResponseHeaderById = async (id: number) => {
  try {
    return api.get(`http://localhost:3000/evaluate/responses/header/${id}`);
  } catch (error) {
    showError('ไม่สามารถโหลดหัวตารางผลลัพธ์ได้');
    throw error;
  }
};

export default {
  getFormById,
  getAll,
  getOne,
  createForm,
  updateForm,
  deleteForm,
  getResponseById,
  getResponseHeaderById,
};
